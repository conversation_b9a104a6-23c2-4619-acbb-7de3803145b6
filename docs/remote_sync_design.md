# 远程同步功能设计方案 (v9 - 客户-设备-基站板三层架构)

## 1. 概述

本方案基于明确的业务逻辑，设计了客户-设备-基站板三层架构的远程同步系统。通过 `siteId`、`deviceId`、`sn` 三层标识符，实现了从客户级别到基站板级别的完整数据管理和实时同步功能。

## 2. 核心业务模型

### 2.1. 三层业务架构

```
客户 (siteId)
├── 设备1 (deviceId)
│   ├── 基站板A (sn)
│   ├── 基站板B (sn)
│   └── 基站板C (sn)
├── 设备2 (deviceId)
│   ├── 基站板D (sn)
│   └── 基站板E (sn)
└── 设备3 (deviceId)
    └── 基站板F (sn)
```

### 2.2. 三层架构可视化图

```mermaid
graph TD
    A[客户管理系统] --> B[客户A - siteId: customer_huawei_5g_lab]
    A --> C[客户B - siteId: customer_zte_4g_test]
    A --> D[客户C - siteId: customer_nokia_indoor]

    B --> E[设备1 - deviceId: device_basestation_001]
    B --> F[设备2 - deviceId: device_basestation_002]
    B --> G[设备3 - deviceId: device_repeater_001]

    E --> H[基站板A - sn: BST2024001234567<br/>别名: 1号槽位功放板]
    E --> I[基站板B - sn: BST2024001234568<br/>别名: 2号槽位射频板]
    E --> J[基站板C - sn: BST2024001234569<br/>别名: 3号槽位控制板]

    F --> K[基站板D - sn: BST2024001234570<br/>别名: 主功放板]
    F --> L[基站板E - sn: BST2024001234571<br/>别名: 备用功放板]

    G --> M[基站板F - sn: BST2024001234572<br/>别名: 中继板]

    C --> N[设备4 - deviceId: device_basestation_003]
    N --> O[基站板G - sn: BST2024001234573]

    D --> P[设备5 - deviceId: device_indoor_001]
    P --> Q[基站板H - sn: BST2024001234574]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style N fill:#e8f5e8
    style P fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#fff3e0
    style K fill:#fff3e0
    style L fill:#fff3e0
    style M fill:#fff3e0
    style O fill:#fff3e0
    style Q fill:#fff3e0
```

### 2.2. 标识符定义与业务含义

#### `siteId` (客户ID)
- **业务含义**: 代表一个客户，是数据隔离和权限管理的最高层级
- **定义**: 全局唯一的客户标识符，用于区分不同的客户或项目
- **格式建议**: `customer_<客户名称>_<项目名称>` (如: `customer_huawei_5g_lab`)
- **赋值方法**: 
  - 由系统管理员在客户签约时通过管理后台创建
  - 每个客户分配唯一的 `siteId`
  - 与客户的合同信息、联系人信息等业务数据关联
- **数据隔离**: 不同 `siteId` 的数据完全隔离，确保客户数据安全

#### `deviceId` (设备ID)
- **业务含义**: 代表客户拥有的一台物理设备（如一台基站设备）
- **定义**: 在客户范围内（`siteId`）唯一的设备标识符
- **格式建议**: `device_<设备类型>_<序号>` (如: `device_basestation_001`)
- **赋值方法**:
  1. **设备发现**: `udp_server` 扫描本地网络，发现新设备的硬件标识 (`hwId`)
  2. **设备上报**: 将未注册的设备信息上报给 `mcp_server`
  3. **设备绑定**: 客户管理员在 WebApp 中为设备分配有意义的 `deviceId`
  4. **设备激活**: 完成绑定后，设备开始正常工作并接受管理

#### `sn` (基站板序列号)
- **业务含义**: 代表设备内部的一块基站板卡，是最小的可管理单元
- **定义**: 基站板的硬件序列号，由制造商烧录，全局唯一
- **格式**: 制造商定义的序列号格式 (如: `BST2024001234567`)
- **赋值方法**:
  - 由 `udp_server` 通过设备通信协议自动读取
  - **不可人工修改**，确保与物理硬件的一一对应
  - 系统自动关联到对应的 `deviceId`
  - 管理员可设置易读的别名 (如: "1号槽位功放板")

## 3. 数据库存储方案

### 3.1. 客户表 (Sites)
```sql
CREATE TABLE Sites (
    siteId VARCHAR(255) PRIMARY KEY,
    customerName VARCHAR(255) NOT NULL,
    projectName VARCHAR(255),
    contactPerson VARCHAR(255),
    contactEmail VARCHAR(255),
    contactPhone VARCHAR(255),
    contractNumber VARCHAR(255),
    tempId VARCHAR(255) UNIQUE, -- 用于绑定流程的临时ID
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2. 设备表 (Devices)
```sql
CREATE TABLE Devices (
    id SERIAL PRIMARY KEY,
    deviceId VARCHAR(255) NOT NULL,
    siteId VARCHAR(255) NOT NULL REFERENCES Sites(siteId) ON DELETE CASCADE,
    deviceName VARCHAR(255), -- 设备别名
    deviceType VARCHAR(100), -- 设备类型：basestation, repeater, etc.
    hwId VARCHAR(255) UNIQUE, -- 设备硬件标识，如MAC地址
    ipAddress VARCHAR(45), -- 设备IP地址
    port INTEGER, -- 设备端口
    status ENUM('online', 'offline', 'error') DEFAULT 'offline',
    lastHeartbeat TIMESTAMP,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(siteId, deviceId) -- 确保在客户内deviceId唯一
);
```

### 3.3. 基站板表 (BaseStationBoards)
```sql
CREATE TABLE BaseStationBoards (
    sn VARCHAR(255) PRIMARY KEY,
    deviceId INTEGER NOT NULL REFERENCES Devices(id) ON DELETE CASCADE,
    alias VARCHAR(255), -- 基站板别名，如 "1号槽位功放板"
    boardType VARCHAR(100), -- 板卡类型：power_amplifier, rf_board, etc.
    slotNumber INTEGER, -- 槽位号
    frequency DECIMAL(10,3), -- 工作频率 (MHz)
    power DECIMAL(5,2), -- 功率 (dBm)
    temperature DECIMAL(5,2), -- 温度 (°C)
    status ENUM('normal', 'warning', 'error', 'offline') DEFAULT 'offline',
    lastUpdate TIMESTAMP,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.4. 操作日志表 (OperationLogs)
```sql
CREATE TABLE OperationLogs (
    id SERIAL PRIMARY KEY,
    siteId VARCHAR(255) NOT NULL REFERENCES Sites(siteId),
    deviceId VARCHAR(255),
    sn VARCHAR(255),
    operation VARCHAR(100) NOT NULL, -- restart, power_adjust, config_change
    operatorId VARCHAR(255), -- 操作员ID
    operatorName VARCHAR(255), -- 操作员姓名
    parameters JSON, -- 操作参数
    result ENUM('success', 'failed', 'pending') DEFAULT 'pending',
    errorMessage TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 4. NATS 主题设计

### 4.1. 主题命名规范
```
# 客户级别主题
sites.{siteId}.status           # 客户站点状态
sites.{siteId}.devices          # 设备列表更新

# 设备级别主题  
sites.{siteId}.devices.{deviceId}.heartbeat    # 设备心跳
sites.{siteId}.devices.{deviceId}.status       # 设备状态
sites.{siteId}.devices.{deviceId}.command      # 设备命令

# 基站板级别主题
sites.{siteId}.devices.{deviceId}.boards.{sn}.status    # 基站板状态
sites.{siteId}.devices.{deviceId}.boards.{sn}.command   # 基站板命令
sites.{siteId}.devices.{deviceId}.boards.{sn}.data      # 基站板数据

# 绑定流程主题
onboarding.announce                    # 新设备广播
onboarding.configure.{tempId}          # 配置下发
```

### 4.2. NATS消息流架构图

```mermaid
graph LR
    subgraph "客户现场"
        UDP[UDP Server<br/>siteId: customer_huawei]
        DEV1[设备1<br/>deviceId: device_001]
        DEV2[设备2<br/>deviceId: device_002]
        BST1[基站板<br/>sn: BST001]
        BST2[基站板<br/>sn: BST002]
        BST3[基站板<br/>sn: BST003]

        DEV1 --> BST1
        DEV1 --> BST2
        DEV2 --> BST3
        UDP --> DEV1
        UDP --> DEV2
    end

    subgraph "云端服务"
        NATS[NATS 消息中间件]
        MCP[MCP Server]
        DB[(数据库)]

        MCP --> DB
        MCP <--> NATS
    end

    subgraph "Web应用"
        WEB1[WebApp 实例1<br/>北京]
        WEB2[WebApp 实例2<br/>上海]
        WEB3[WebApp 实例3<br/>深圳]

        WEB1 <--> NATS
        WEB2 <--> NATS
        WEB3 <--> NATS
    end

    UDP <--> NATS

    subgraph "NATS 主题"
        T1[sites.customer_huawei.status]
        T2[sites.customer_huawei.devices.device_001.heartbeat]
        T3[sites.customer_huawei.devices.device_001.boards.BST001.status]
        T4[sites.customer_huawei.devices.device_001.boards.BST001.command]
        T5[sites.customer_huawei.devices.device_002.boards.BST003.data]
    end

    NATS --> T1
    NATS --> T2
    NATS --> T3
    NATS --> T4
    NATS --> T5

    style UDP fill:#e3f2fd
    style MCP fill:#e8f5e8
    style NATS fill:#fff3e0
    style DB fill:#fce4ec
    style WEB1 fill:#f3e5f5
    style WEB2 fill:#f3e5f5
    style WEB3 fill:#f3e5f5
```

### 4.3. 消息格式示例
```json
// 基站板状态消息
{
  "siteId": "customer_huawei_5g_lab",
  "deviceId": "device_basestation_001", 
  "sn": "BST2024001234567",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "frequency": 2600.5,
    "power": 23.5,
    "temperature": 45.2,
    "status": "normal",
    "interference": -85.3,
    "pci": 156
  }
}

// 功率调节命令
{
  "siteId": "customer_huawei_5g_lab",
  "deviceId": "device_basestation_001",
  "sn": "BST2024001234567", 
  "command": "power_adjust",
  "parameters": {
    "targetPower": 20.0,
    "operatorId": "admin001",
    "operatorName": "张三"
  },
  "timestamp": "2024-01-15T10:35:00Z"
}
```

## 5. 动态绑定工作流

### 5.1. 动态绑定流程图

```mermaid
sequenceDiagram
    participant Admin as 系统管理员
    participant WebApp as WebApp界面
    participant MCP as MCP Server
    participant DB as 数据库
    participant NATS as NATS消息中间件
    participant UDP as UDP Server(现场)
    participant Device as 基站设备

    Note over Admin,Device: 1. 客户绑定阶段
    Admin->>WebApp: 创建新客户
    WebApp->>MCP: 提交客户信息
    MCP->>DB: 创建Sites记录
    DB-->>MCP: 返回siteId
    MCP-->>WebApp: 确认创建成功

    Note over Admin,Device: 2. UDP服务器绑定阶段
    UDP->>NATS: 发布到onboarding.announce<br/>{tempId: "MAC_123456"}
    NATS->>MCP: 转发绑定请求
    MCP->>DB: 记录待绑定设备
    MCP->>WebApp: 通知有新设备待绑定

    Admin->>WebApp: 查看待绑定设备列表
    Admin->>WebApp: 选择设备并分配siteId
    WebApp->>MCP: 提交绑定请求<br/>{tempId, siteId}
    MCP->>DB: 更新绑定关系
    MCP->>NATS: 发布配置到onboarding.configure.MAC_123456<br/>{siteId: "customer_huawei"}
    NATS->>UDP: 下发配置
    UDP->>UDP: 保存siteId到本地配置
    UDP->>NATS: 确认配置成功

    Note over Admin,Device: 3. 设备发现与绑定阶段
    UDP->>Device: 扫描本地网络
    Device-->>UDP: 返回设备信息{hwId, ip, port}
    UDP->>NATS: 发布到sites.customer_huawei.devices<br/>{hwId, status: "discovered"}
    NATS->>MCP: 转发设备信息
    MCP->>DB: 记录未绑定设备
    MCP->>WebApp: 通知有新设备发现

    Admin->>WebApp: 查看未绑定设备
    Admin->>WebApp: 为设备分配deviceId
    WebApp->>MCP: 提交设备绑定<br/>{hwId, deviceId, siteId}
    MCP->>DB: 创建Devices记录
    MCP->>NATS: 发布设备激活命令
    NATS->>UDP: 转发激活命令
    UDP->>Device: 激活设备管理

    Note over Admin,Device: 4. 基站板自动发现阶段
    Device->>Device: 扫描内部基站板
    Device-->>UDP: 返回基站板信息{sn, type, slot}
    UDP->>NATS: 发布到sites.customer_huawei.devices.device_001.boards<br/>{sn, boardInfo}
    NATS->>MCP: 转发基站板信息
    MCP->>DB: 自动创建BaseStationBoards记录
    MCP->>WebApp: 更新界面显示

    Admin->>WebApp: 为基站板设置别名
    WebApp->>MCP: 更新基站板别名
    MCP->>DB: 更新别名信息

    Note over Admin,Device: 绑定完成，系统正常运行
```

### 5.2. 客户绑定流程详述
1. **新客户注册**: 系统管理员在后台创建客户记录，分配 `siteId`
2. **UDP服务器部署**: 在客户现场部署 `udp_server`，初始状态为未配置
3. **在线绑定**: 通过 `onboarding` 主题完成 UDP服务器与客户的绑定
4. **配置下发**: 将 `siteId` 配置下发给 UDP服务器并持久化

### 5.2. 设备绑定流程详述
1. **设备发现**: UDP服务器扫描并发现新设备
2. **设备上报**: 将设备硬件信息上报给 MCP服务器
3. **设备命名**: 客户管理员为设备分配有意义的 `deviceId`
4. **设备激活**: 完成绑定，设备开始正常工作

### 5.3. 基站板自动发现详述
1. **板卡扫描**: 设备启动时自动扫描内部基站板
2. **序列号读取**: 通过硬件接口读取基站板序列号
3. **自动注册**: 将基站板信息自动注册到数据库
4. **别名设置**: 管理员可为基站板设置易读别名

## 6. 实时同步机制

### 6.1. 状态同步
- 所有基站板状态变化通过 NATS 实时广播
- WebApp 订阅相关主题，实时更新界面显示
- 支持多个 WebApp 实例同时连接，状态保持一致

### 6.2. 操作同步
- 任何操作（重启、功率调节等）都会广播给所有订阅者
- 操作结果实时反馈，确保所有界面同步更新
- 操作日志完整记录，支持审计和回溯

### 6.3. 权限控制
- 基于 `siteId` 的数据隔离，客户只能访问自己的数据
- 操作权限分级：查看、操作、管理
- 所有操作记录操作员信息，确保可追溯性

## 7. 技术实现要点

### 7.1. UDP服务器状态机
- **未配置状态**: 使用临时ID，订阅 `onboarding` 主题
- **已配置状态**: 使用正式 `siteId`，订阅业务主题
- **配置持久化**: 将 `siteId` 写入本地配置文件

### 7.2. MCP服务器功能
- 绑定流程管理：处理设备发现和绑定请求
- 数据库管理：维护三层数据关系
- 消息路由：基于标识符进行精确消息路由
- 权限验证：确保数据访问安全

### 7.3. WebApp界面
- 客户管理：客户信息维护和权限管理
- 设备管理：设备发现、绑定和状态监控
- 基站板管理：基站板状态监控和操作控制
- 实时同步：多实例间的实时状态同步

## 8. 扩展性考虑

### 8.1. 水平扩展
- 支持单个客户拥有多个设备
- 支持单个设备拥有多块基站板
- 数据库分片策略：按 `siteId` 进行分片

### 8.2. 功能扩展
- 支持新的设备类型和基站板类型
- 支持更多操作类型和参数配置
- 支持告警和通知机制

### 8.3. 性能优化
- NATS 集群部署，提高消息处理能力
- 数据库读写分离，提高查询性能
- 缓存机制，减少数据库访问压力
