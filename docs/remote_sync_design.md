# 远程同步功能设计方案 (v9 - 客户-设备-基站板三层架构)

## 1. 概述

本方案基于明确的业务逻辑，设计了客户-设备-基站板三层架构的远程同步系统。通过 `siteId`、`deviceId`、`sn` 三层标识符，实现了从客户级别到基站板级别的完整数据管理和实时同步功能。

## 2. 核心业务模型

### 2.1. 三层业务架构

```
客户 (siteId)
├── 设备1 (deviceId)
│   ├── 基站板A (sn)
│   ├── 基站板B (sn)
│   └── 基站板C (sn)
├── 设备2 (deviceId)
│   ├── 基站板D (sn)
│   └── 基站板E (sn)
└── 设备3 (deviceId)
    └── 基站板F (sn)
```

### 2.2. 三层架构可视化图

```mermaid
graph TD
    A[客户管理系统] --> B[客户A - siteId: customer_huawei_5g_lab]
    A --> C[客户B - siteId: customer_zte_4g_test]
    A --> D[客户C - siteId: customer_nokia_indoor]

    B --> E[设备1 - deviceId: device_basestation_001]
    B --> F[设备2 - deviceId: device_basestation_002]
    B --> G[设备3 - deviceId: device_repeater_001]

    E --> H[基站板A - sn: BST2024001234567<br/>别名: 1号槽位功放板]
    E --> I[基站板B - sn: BST2024001234568<br/>别名: 2号槽位射频板]
    E --> J[基站板C - sn: BST2024001234569<br/>别名: 3号槽位控制板]

    F --> K[基站板D - sn: BST2024001234570<br/>别名: 主功放板]
    F --> L[基站板E - sn: BST2024001234571<br/>别名: 备用功放板]

    G --> M[基站板F - sn: BST2024001234572<br/>别名: 中继板]

    C --> N[设备4 - deviceId: device_basestation_003]
    N --> O[基站板G - sn: BST2024001234573]

    D --> P[设备5 - deviceId: device_indoor_001]
    P --> Q[基站板H - sn: BST2024001234574]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style N fill:#e8f5e8
    style P fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#fff3e0
    style K fill:#fff3e0
    style L fill:#fff3e0
    style M fill:#fff3e0
    style O fill:#fff3e0
    style Q fill:#fff3e0
```

### 2.2. 标识符定义与业务含义

#### `siteId` (客户ID)
- **业务含义**: 代表一个客户，是数据隔离和权限管理的最高层级
- **定义**: 全局唯一的客户标识符，用于区分不同的客户或项目
- **格式建议**: `customer_<客户名称>_<项目名称>` (如: `customer_huawei_5g_lab`)
- **赋值方法**: 
  - 由系统管理员在客户签约时通过管理后台创建
  - 每个客户分配唯一的 `siteId`
  - 与客户的合同信息、联系人信息等业务数据关联
- **数据隔离**: 不同 `siteId` 的数据完全隔离，确保客户数据安全

#### `deviceId` (设备ID)
- **业务含义**: 代表客户拥有的一台物理设备（如一台基站设备）
- **定义**: 在客户范围内（`siteId`）唯一的设备标识符
- **格式建议**: `device_<设备类型>_<序号>` (如: `device_basestation_001`)
- **赋值方法**:
  1. **设备发现**: `udp_server` 扫描本地网络，发现新设备的硬件标识 (`hwId`)
  2. **设备上报**: 将未注册的设备信息上报给 `mcp_server`
  3. **设备绑定**: 客户管理员在 WebApp 中为设备分配有意义的 `deviceId`
  4. **设备激活**: 完成绑定后，设备开始正常工作并接受管理

#### `sn` (基站板序列号)
- **业务含义**: 代表设备内部的一块基站板卡，是最小的可管理单元
- **定义**: 基站板的硬件序列号，由制造商烧录，全局唯一
- **格式**: 制造商定义的序列号格式 (如: `BST2024001234567`)
- **赋值方法**:
  - 由 `udp_server` 通过设备通信协议自动读取
  - **不可人工修改**，确保与物理硬件的一一对应
  - 系统自动关联到对应的 `deviceId`
  - 管理员可设置易读的别名 (如: "1号槽位功放板")

## 3. 数据库存储方案

### 3.1. 客户表 (Sites)
```sql
CREATE TABLE Sites (
    siteId VARCHAR(255) PRIMARY KEY,
    customerName VARCHAR(255) NOT NULL,
    projectName VARCHAR(255),
    contactPerson VARCHAR(255),
    contactEmail VARCHAR(255),
    contactPhone VARCHAR(255),
    contractNumber VARCHAR(255),
    tempId VARCHAR(255) UNIQUE, -- 用于绑定流程的临时ID
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2. 设备表 (Devices)
```sql
CREATE TABLE Devices (
    id SERIAL PRIMARY KEY,
    deviceId VARCHAR(255) NOT NULL,
    siteId VARCHAR(255) NOT NULL REFERENCES Sites(siteId) ON DELETE CASCADE,
    deviceName VARCHAR(255), -- 设备别名
    deviceType VARCHAR(100), -- 设备类型：basestation, repeater, etc.
    hwId VARCHAR(255) UNIQUE, -- 设备硬件标识，如MAC地址
    ipAddress VARCHAR(45), -- 设备IP地址
    port INTEGER, -- 设备端口
    status ENUM('online', 'offline', 'error') DEFAULT 'offline',
    lastHeartbeat TIMESTAMP,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(siteId, deviceId) -- 确保在客户内deviceId唯一
);
```

### 3.3. 基站板表 (BaseStationBoards)
```sql
CREATE TABLE BaseStationBoards (
    sn VARCHAR(255) PRIMARY KEY,
    deviceId INTEGER NOT NULL REFERENCES Devices(id) ON DELETE CASCADE,
    alias VARCHAR(255), -- 基站板别名，如 "1号槽位功放板"
    boardType VARCHAR(100), -- 板卡类型：power_amplifier, rf_board, etc.
    slotNumber INTEGER, -- 槽位号
    frequency DECIMAL(10,3), -- 工作频率 (MHz)
    power DECIMAL(5,2), -- 功率 (dBm)
    temperature DECIMAL(5,2), -- 温度 (°C)
    status ENUM('normal', 'warning', 'error', 'offline') DEFAULT 'offline',
    lastUpdate TIMESTAMP,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.4. 操作日志表 (OperationLogs)
```sql
CREATE TABLE OperationLogs (
    id SERIAL PRIMARY KEY,
    siteId VARCHAR(255) NOT NULL REFERENCES Sites(siteId),
    deviceId VARCHAR(255),
    sn VARCHAR(255),
    operation VARCHAR(100) NOT NULL, -- restart, power_adjust, config_change
    operatorId VARCHAR(255), -- 操作员ID
    operatorName VARCHAR(255), -- 操作员姓名
    parameters JSON, -- 操作参数
    result ENUM('success', 'failed', 'pending') DEFAULT 'pending',
    errorMessage TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 4. NATS 主题设计

### 4.1. 主题命名规范
```
# 客户级别主题
sites.{siteId}.status           # 客户站点状态
sites.{siteId}.devices          # 设备列表更新

# 设备级别主题  
sites.{siteId}.devices.{deviceId}.heartbeat    # 设备心跳
sites.{siteId}.devices.{deviceId}.status       # 设备状态
sites.{siteId}.devices.{deviceId}.command      # 设备命令

# 基站板级别主题
sites.{siteId}.devices.{deviceId}.boards.{sn}.status    # 基站板状态
sites.{siteId}.devices.{deviceId}.boards.{sn}.command   # 基站板命令
sites.{siteId}.devices.{deviceId}.boards.{sn}.data      # 基站板数据

# 绑定流程主题
onboarding.announce                    # 新设备广播
onboarding.configure.{tempId}          # 配置下发
```

### 4.2. NATS消息流架构图

```mermaid
graph LR
    subgraph "客户现场"
        UDP[UDP Server<br/>siteId: customer_huawei]
        DEV1[设备1<br/>deviceId: device_001]
        DEV2[设备2<br/>deviceId: device_002]
        BST1[基站板<br/>sn: BST001]
        BST2[基站板<br/>sn: BST002]
        BST3[基站板<br/>sn: BST003]

        DEV1 --> BST1
        DEV1 --> BST2
        DEV2 --> BST3
        UDP --> DEV1
        UDP --> DEV2
    end

    subgraph "云端服务"
        NATS[NATS 消息中间件]
        MCP[MCP Server]
        DB[(数据库)]

        MCP --> DB
        MCP <--> NATS
    end

    subgraph "Web应用"
        WEB1[WebApp 实例1<br/>北京]
        WEB2[WebApp 实例2<br/>上海]
        WEB3[WebApp 实例3<br/>深圳]

        WEB1 <--> NATS
        WEB2 <--> NATS
        WEB3 <--> NATS
    end

    UDP <--> NATS

    subgraph "NATS 主题"
        T1[sites.customer_huawei.status]
        T2[sites.customer_huawei.devices.device_001.heartbeat]
        T3[sites.customer_huawei.devices.device_001.boards.BST001.status]
        T4[sites.customer_huawei.devices.device_001.boards.BST001.command]
        T5[sites.customer_huawei.devices.device_002.boards.BST003.data]
    end

    NATS --> T1
    NATS --> T2
    NATS --> T3
    NATS --> T4
    NATS --> T5

    style UDP fill:#e3f2fd
    style MCP fill:#e8f5e8
    style NATS fill:#fff3e0
    style DB fill:#fce4ec
    style WEB1 fill:#f3e5f5
    style WEB2 fill:#f3e5f5
    style WEB3 fill:#f3e5f5
```

### 4.3. 消息格式示例
```json
// 基站板状态消息
{
  "siteId": "customer_huawei_5g_lab",
  "deviceId": "device_basestation_001", 
  "sn": "BST2024001234567",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "frequency": 2600.5,
    "power": 23.5,
    "temperature": 45.2,
    "status": "normal",
    "interference": -85.3,
    "pci": 156
  }
}

// 功率调节命令
{
  "siteId": "customer_huawei_5g_lab",
  "deviceId": "device_basestation_001",
  "sn": "BST2024001234567", 
  "command": "power_adjust",
  "parameters": {
    "targetPower": 20.0,
    "operatorId": "admin001",
    "operatorName": "张三"
  },
  "timestamp": "2024-01-15T10:35:00Z"
}
```

## 5. 动态绑定工作流

### 5.1. 动态绑定流程图

```mermaid
sequenceDiagram
    participant Admin as 系统管理员
    participant WebApp as WebApp界面
    participant MCP as MCP Server
    participant DB as 数据库
    participant NATS as NATS消息中间件
    participant UDP as UDP Server(现场)
    participant Device as 基站设备

    Note over Admin,Device: 1. 客户绑定阶段
    Admin->>WebApp: 创建新客户
    WebApp->>MCP: 提交客户信息
    MCP->>DB: 创建Sites记录
    DB-->>MCP: 返回siteId
    MCP-->>WebApp: 确认创建成功

    Note over Admin,Device: 2. UDP服务器绑定阶段
    UDP->>NATS: 发布到onboarding.announce<br/>{tempId: "MAC_123456"}
    NATS->>MCP: 转发绑定请求
    MCP->>DB: 记录待绑定设备
    MCP->>WebApp: 通知有新设备待绑定

    Admin->>WebApp: 查看待绑定设备列表
    Admin->>WebApp: 选择设备并分配siteId
    WebApp->>MCP: 提交绑定请求<br/>{tempId, siteId}
    MCP->>DB: 更新绑定关系
    MCP->>NATS: 发布配置到onboarding.configure.MAC_123456<br/>{siteId: "customer_huawei"}
    NATS->>UDP: 下发配置
    UDP->>UDP: 保存siteId到本地配置
    UDP->>NATS: 确认配置成功

    Note over Admin,Device: 3. 设备发现与绑定阶段
    UDP->>Device: 扫描本地网络
    Device-->>UDP: 返回设备信息{hwId, ip, port}
    UDP->>NATS: 发布到sites.customer_huawei.devices<br/>{hwId, status: "discovered"}
    NATS->>MCP: 转发设备信息
    MCP->>DB: 记录未绑定设备
    MCP->>WebApp: 通知有新设备发现

    Admin->>WebApp: 查看未绑定设备
    Admin->>WebApp: 为设备分配deviceId
    WebApp->>MCP: 提交设备绑定<br/>{hwId, deviceId, siteId}
    MCP->>DB: 创建Devices记录
    MCP->>NATS: 发布设备激活命令
    NATS->>UDP: 转发激活命令
    UDP->>Device: 激活设备管理

    Note over Admin,Device: 4. 基站板自动发现阶段
    Device->>Device: 扫描内部基站板
    Device-->>UDP: 返回基站板信息{sn, type, slot}
    UDP->>NATS: 发布到sites.customer_huawei.devices.device_001.boards<br/>{sn, boardInfo}
    NATS->>MCP: 转发基站板信息
    MCP->>DB: 自动创建BaseStationBoards记录
    MCP->>WebApp: 更新界面显示

    Admin->>WebApp: 为基站板设置别名
    WebApp->>MCP: 更新基站板别名
    MCP->>DB: 更新别名信息

    Note over Admin,Device: 绑定完成，系统正常运行
```

### 5.2. 客户绑定流程详述
1. **新客户注册**: 系统管理员在后台创建客户记录，分配 `siteId`
2. **UDP服务器部署**: 在客户现场部署 `udp_server`，初始状态为未配置
3. **在线绑定**: 通过 `onboarding` 主题完成 UDP服务器与客户的绑定
4. **配置下发**: 将 `siteId` 配置下发给 UDP服务器并持久化

### 5.3. 设备绑定流程详述
1. **设备发现**: UDP服务器扫描并发现新设备
2. **设备上报**: 将设备硬件信息上报给 MCP服务器
3. **设备命名**: 客户管理员为设备分配有意义的 `deviceId`
4. **设备激活**: 完成绑定，设备开始正常工作

### 5.4. 基站板自动发现详述
1. **板卡扫描**: 设备启动时自动扫描内部基站板
2. **序列号读取**: 通过硬件接口读取基站板序列号
3. **自动注册**: 将基站板信息自动注册到数据库
4. **别名设置**: 管理员可为基站板设置易读别名

## 6. 实时同步机制

### 6.1. 状态同步
- 所有基站板状态变化通过 NATS 实时广播
- WebApp 订阅相关主题，实时更新界面显示
- 支持多个 WebApp 实例同时连接，状态保持一致

### 6.2. 操作同步
- 任何操作（重启、功率调节等）都会广播给所有订阅者
- 操作结果实时反馈，确保所有界面同步更新
- 操作日志完整记录，支持审计和回溯

### 6.3. 权限控制
- 基于 `siteId` 的数据隔离，客户只能访问自己的数据
- 操作权限分级：查看、操作、管理
- 所有操作记录操作员信息，确保可追溯性

## 7. 技术实现要点

### 7.1. UDP服务器状态机
- **未配置状态**: 使用临时ID，订阅 `onboarding` 主题
- **已配置状态**: 使用正式 `siteId`，订阅业务主题
- **配置持久化**: 将 `siteId` 写入本地配置文件

### 7.2. MCP服务器功能
- 绑定流程管理：处理设备发现和绑定请求
- 数据库管理：维护三层数据关系
- 消息路由：基于标识符进行精确消息路由
- 权限验证：确保数据访问安全

### 7.3. WebApp界面
- 客户管理：客户信息维护和权限管理
- 设备管理：设备发现、绑定和状态监控
- 基站板管理：基站板状态监控和操作控制
- 实时同步：多实例间的实时状态同步

## 8. 安全与可靠性设计

### 8.1. 数据安全
- **传输加密**: 所有NATS通信使用TLS加密
- **身份认证**: JWT令牌验证客户端身份
- **权限控制**: 基于角色的访问控制(RBAC)
- **数据隔离**: 严格的客户数据隔离机制

### 8.2. 故障恢复
- **断线重连**: UDP服务器自动重连机制
- **消息持久化**: 关键消息持久化存储
- **状态恢复**: 系统重启后自动恢复状态
- **备份策略**: 定期数据备份和恢复测试

### 8.3. 监控告警
- **健康检查**: 各组件健康状态监控
- **性能监控**: 消息延迟、吞吐量监控
- **异常告警**: 设备离线、操作失败告警
- **日志审计**: 完整的操作日志和审计跟踪

## 9. 性能优化策略

### 9.1. 消息优化
- **批量处理**: 状态更新消息批量发送
- **压缩传输**: 大数据包启用压缩
- **主题分片**: 高频主题按设备分片
- **缓存策略**: 热点数据内存缓存

### 9.2. 数据库优化
- **索引优化**: 关键查询字段建立索引
- **分区表**: 按时间分区存储历史数据
- **读写分离**: 查询和写入分离部署
- **连接池**: 数据库连接池管理

### 9.3. 网络优化
- **CDN加速**: 静态资源CDN分发
- **负载均衡**: 多实例负载均衡
- **就近接入**: 多地域部署就近访问
- **带宽控制**: 消息传输带宽限制

## 10. 扩展性考虑

### 10.1. 水平扩展
- 支持单个客户拥有多个设备
- 支持单个设备拥有多块基站板
- 数据库分片策略：按 `siteId` 进行分片
- 微服务架构：各组件独立扩展

### 10.2. 功能扩展
- 支持新的设备类型和基站板类型
- 支持更多操作类型和参数配置
- 支持告警和通知机制
- 支持第三方系统集成API

### 10.3. 技术演进
- 容器化部署：Docker + Kubernetes
- 服务网格：Istio流量管理
- 事件驱动：Event Sourcing模式
- 机器学习：智能故障预测

## 11. 部署架构建议

### 11.1. 生产环境架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   负载均衡器     │    │   Web应用集群    │    │   API网关       │
│   (Nginx/HAProxy)│    │   (多实例)      │    │   (Kong/Zuul)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   NATS集群      │    │   MCP服务集群    │    │   数据库集群     │
│   (3节点)       │    │   (多实例)      │    │   (主从+读写分离) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 11.2. 容器化部署
- **Docker镜像**: 各组件独立镜像
- **Kubernetes**: 容器编排管理
- **Helm Charts**: 应用包管理
- **CI/CD**: 自动化部署流水线

### 11.3. 监控体系
- **Prometheus**: 指标收集
- **Grafana**: 可视化监控
- **ELK Stack**: 日志分析
- **Jaeger**: 分布式链路追踪

## 12. 实施建议

### 12.1. 分阶段实施
1. **第一阶段**: 核心功能MVP（客户-设备-基站板管理）
2. **第二阶段**: 实时同步和动态绑定
3. **第三阶段**: 高级功能（告警、分析、优化）
4. **第四阶段**: 企业级功能（多租户、API开放）

### 12.2. 技术选型建议
- **后端**: Node.js + TypeScript
- **数据库**: PostgreSQL + Redis
- **消息队列**: NATS.io
- **前端**: Vue.js 3 + TypeScript
- **部署**: Docker + Kubernetes

### 12.3. 团队配置建议
- **架构师**: 1名（系统设计和技术决策）
- **后端开发**: 2-3名（MCP服务器、UDP服务器）
- **前端开发**: 2名（WebApp界面）
- **运维工程师**: 1名（部署和监控）
- **测试工程师**: 1名（功能和性能测试）

## 13. 技术风险评估与解决方案

### 13.1. 网络连接风险
**风险**: 客户现场网络不稳定，UDP服务器频繁断线
**解决方案**:
- 实现指数退避重连机制
- 本地缓存关键状态数据
- 离线模式支持基本操作
- 网络质量监控和告警

### 13.2. 数据一致性风险
**风险**: 分布式环境下数据不一致
**解决方案**:
- 使用事务保证关键操作原子性
- 实现最终一致性模型
- 版本控制和冲突解决机制
- 定期数据校验和修复

### 13.3. 性能瓶颈风险
**风险**: 大量设备同时在线导致性能问题
**解决方案**:
- 消息限流和背压控制
- 数据库查询优化和索引
- 缓存热点数据
- 水平扩展和负载均衡

### 13.4. 安全漏洞风险
**风险**: 客户数据泄露或未授权访问
**解决方案**:
- 端到端加密传输
- 严格的身份认证和授权
- 定期安全审计和渗透测试
- 数据脱敏和访问日志

### 13.5. 运维复杂性风险
**风险**: 系统复杂度高，运维困难
**解决方案**:
- 完善的监控和告警体系
- 自动化部署和回滚
- 详细的运维文档和手册
- 故障演练和应急预案

## 14. 总结与展望

### 14.1. 技术方案对比分析

```mermaid
graph TB
    subgraph "传统方案"
        A1[轮询机制]
        A2[单体架构]
        A3[文件配置]
        A4[手动运维]

        A1 --> A5[延迟高]
        A2 --> A6[扩展性差]
        A3 --> A7[配置复杂]
        A4 --> A8[效率低]
    end

    subgraph "优化方案"
        B1[实时推送]
        B2[微服务架构]
        B3[动态绑定]
        B4[自动化运维]

        B1 --> B5[实时响应]
        B2 --> B6[高扩展性]
        B3 --> B7[零配置]
        B4 --> B8[高效率]
    end

    subgraph "技术对比"
        C1[响应时间: 秒级 → 毫秒级]
        C2[扩展能力: 百台 → 万台]
        C3[部署复杂度: 高 → 低]
        C4[运维成本: 高 → 低]
        C5[可靠性: 中 → 高]
    end

    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3
    A4 -.-> B4

    B5 --> C1
    B6 --> C2
    B7 --> C3
    B8 --> C4

    style A1 fill:#ffebee
    style A2 fill:#ffebee
    style A3 fill:#ffebee
    style A4 fill:#ffebee
    style B1 fill:#e8f5e8
    style B2 fill:#e8f5e8
    style B3 fill:#e8f5e8
    style B4 fill:#e8f5e8
    style C1 fill:#e3f2fd
    style C2 fill:#e3f2fd
    style C3 fill:#e3f2fd
    style C4 fill:#e3f2fd
    style C5 fill:#e3f2fd
```

### 14.2. 方案优势
1. **清晰的业务模型**: 三层架构完美映射业务需求
2. **强大的扩展性**: 支持大规模客户和设备接入
3. **实时同步能力**: 多地域实时状态同步
4. **完善的安全机制**: 多层次安全保障
5. **灵活的部署方式**: 支持云原生和传统部署

### 14.2. 技术创新点
1. **动态绑定机制**: 零配置设备自动发现和绑定
2. **分层消息路由**: 基于业务层级的精确消息路由
3. **多租户隔离**: 完全的客户数据隔离
4. **智能故障恢复**: 自动化的故障检测和恢复

### 14.3. 未来发展方向
1. **AI智能化**: 引入机器学习进行故障预测和优化建议
2. **边缘计算**: 在客户现场部署边缘计算节点
3. **5G集成**: 深度集成5G网络管理功能
4. **生态开放**: 构建开放的API生态系统

### 14.4. 预期效果
- **运维效率提升**: 50%以上的运维工作自动化
- **故障响应时间**: 从小时级降低到分钟级
- **客户满意度**: 显著提升客户使用体验
- **业务扩展**: 支持10倍以上的业务规模增长

本技术方案为基站管理系统提供了完整的远程同步解决方案，通过合理的架构设计和技术选型，能够满足当前业务需求并为未来发展奠定坚实基础。
