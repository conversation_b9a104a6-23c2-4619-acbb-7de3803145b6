# 远程同步功能设计方案 (v8 - 动态绑定与数据持久化)

## 1. 概述

本方案在 v7 的基础上，进一步明确了核心数据标识符 (`siteId`, `deviceId`, `sn`) 的定义、赋值流程，并设计了相应的数据库存储模型。目标是确保所有动态绑定的配置和关系都能被持久化，为系统提供稳定、可扩展的数据基础。

## 2. 核心组件与分层模型

- **三层标识符 (`siteId`, `deviceId`, `sn`)**: 系统的核心逻辑标识，详见第3节。
- **`SharedState`**: 与 v6/v7 方案一致，反映了完整的物理层级。

## 3. 关键数据定义与存储

为了确保系统的可扩展性和可维护性，所有核心标识符都必须有明确的定义，并通过数据库进行持久化管理。

### 3.1. 关键数据定义

*   **`siteId` (站点ID)**
    *   **定义**: 一个由用户定义的、全局唯一的、具有业务含义的字符串，用于标识一个物理位置或客户部署环境（例如 `customer-a-factory-floor`, `lab-testing-bench`）。它是一个 `udp_server.js` 实例的管理边界，也是数据隔离的第一层。
    *   **赋值方法**: 在“动态在线绑定”流程中，由系统管理员通过 WebApp 手动输入并分配。系统会确保其唯一性。

*   **`deviceId` (设备ID)**
    *   **定义**: 在一个 `siteId` 范围内唯一的标识符，代表一个物理设备（如一台基站）。它可以是硬件自带的ID（如MAC地址），也可以是管理员分配的逻辑ID。
    *   **赋值方法**:
        1.  **发现**: `udp_server` 在本地网络发现未被认领的设备。
        2.  **上报**: `udp_server` 将发现的设备信息（如硬件标识 `hwId`）上报给 `mcp_server`。
        3.  **绑定**: 管理员在 WebApp 界面为该 `hwId` 分配一个有意义的 `deviceId`（例如 `main-entry-basestation`），并完成绑定。

*   **`sn` (序列号)**
    *   **定义**: 组件级别的唯一标识符，通常是硬件制造商烧录在物理组件（如主板、射频板）上的序列号。它是层级最低、与物理实体最直接关联的ID。
    *   **赋值方法**: 该ID由 `udp_server` 通过与设备通信后**读取**而来，**不可由用户修改**。它随着设备硬件上报，并自动与对应的 `deviceId` 关联。管理员可以为其设置一个易于辨识的“别名”。

### 3.2. 数据库存储方案

所有绑定关系、配置和状态都必须存储在 `mcp_server` 的数据库中（例如 PostgreSQL, SQLite）。

*   **`Sites` 表**: 存储站点的核心信息。
    ```sql
    CREATE TABLE Sites (
        siteId VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        tempId VARCHAR(255) UNIQUE, -- 用于绑定流程的临时ID
        ownerUserId VARCHAR(255), -- 关联到用户/客户
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ```

*   **`Devices` 表**: 存储设备信息，并关联到站点。
    ```sql
    CREATE TABLE Devices (
        id SERIAL PRIMARY KEY,
        deviceId VARCHAR(255) NOT NULL,
        siteId VARCHAR(255) NOT NULL REFERENCES Sites(siteId),
        name VARCHAR(255), -- 设备别名
        hwId VARCHAR(255) UNIQUE, -- 设备硬件标识，如MAC地址
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(siteId, deviceId) -- 确保在站点内deviceId唯一
    );
    ```

*   **`Components` 表**: 存储板卡/组件信息，并关联到设备。
    ```sql
    CREATE TABLE Components (
        sn VARCHAR(255) PRIMARY KEY,
        deviceId INTEGER NOT NULL REFERENCES Devices(id),
        alias VARCHAR(255), -- 组件别名，如 "1号槽位-功放板"
        type VARCHAR(100), -- 组件类型
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    ```

## 4. 新增：动态在线绑定工作流

### 4.1. 新增NATS主题

- **`onboarding.announce`**: “招领”主题。未配置的本地代理在此主题上广播自己的存在。
- **`onboarding.configure.<tempId>`**: `mcp_server` 通过此主题向特定的待绑定代理下发配置信息。

### 4.2. 绑定流程

1.  **代理上线 (Unconfigured State)**
    a.  全新的 `udp_server.js` 在客户现场启动，发现自己没有配置 `siteId`。
    b.  它生成一个临时唯一ID (`tempId`，如MAC地址)，并向 `onboarding.announce` 主题定期发布“存活”消息。

2.  **`mcp_server` 发现与UI呈现**
    a.  `mcp_server` 监听 `onboarding.announce`，将所有待绑定的代理ID及其网络信息，更新到一个“待处理”列表中。
    b.  WebApp的“设备管理”界面从 `mcp_server` 获取并显示这个“待绑定代理”列表。

3.  **用户在线绑定 (UI Interaction)**
    a.  管理员在WebApp界面上看到新代理，点击“绑定”按钮。
    b.  UI引导管理员为此代理设置一个永久的、有意义的 `siteId` (例如 `customer_a_lab`)，并将其与一个用户账户关联。

4.  **云端确认与指令下发**
    a.  WebApp将绑定请求（`tempId` + `siteId`）发送给 `mcp_server`。
    b.  `mcp_server` 在 `Sites` 表中创建或更新一条记录，存储 `siteId` 与 `tempId` 的绑定关系，并关联到相应的用户账户。
    c.  `mcp_server` 向 `onboarding.configure.<tempId>` 主题（例如 `onboarding.configure.mac_addr_12345`）发布一条配置指令。
       ```json
       // mcp_server publishes configuration
       {
         "command": "CONFIGURE",
         "payload": { "siteId": "customer_a_lab" }
       }
       ```

5.  **代理固化配置**
    a.  本地代理从其订阅的配置主题中收到指令，获得了自己的永久 `siteId`。
    b.  **关键步骤**: 代理将这个 `siteId` 写入本地的持久化配置文件（如 `config.json`）中。
    c.  代理断开与 `onboarding.*` 主题的连接，使用新的 `siteId` 重新初始化，并开始在正式主题 `sites.customer_a_lab.*` 上进行通信。

### 4.3. 设备与板卡的绑定

- 此流程可以复用。一旦代理被绑定，它可以扫描本地网络，将发现的、未在 `Devices` 表中注册的设备 `hwId` 上报。这些“未注册设备”会显示在UI中，等待管理员分配 `deviceId` 并进行绑定。同样地，设备下的 `sn` 也会被自动发现并记录到 `Components` 表中。

## 5. 工作流程 (分层寻址)

- **上行与下行数据流**: 与 v6/v7 方案完全一致，所有通信都基于数据库中已定义的 `siteId`, `deviceId`, `sn` 进行精确寻址。

## 6. 技术实现要点

- **`udp_server.js` 状态机**: 本地代理需要有两种工作模式：**未配置模式**（使用 `onboarding` 主题）和**已配置模式**（使用 `sites.*` 主题）。
- **WebApp UI/UX**: 需要新增“设备管理”模块，用于显示、命名和绑定待处理的代理、设备和板卡。
- **`mcp_server` 逻辑**: 需要增加处理 `onboarding` 流程的逻辑，并与数据库（见3.2节）紧密集成，以管理绑定关系和下发配置指令。