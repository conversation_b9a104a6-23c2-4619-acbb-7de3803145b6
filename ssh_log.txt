  "stdout": "<PERSON>_TAG_MAIN_CONTROL_PANEL_PORT =3346\n
             WR_TAG_MAIN_CONTROL_PANEL_IP = ************\n
             WR_TAG_SECOND_CONTROL_PANEL_PORT = 3346\r\n
             WR_TAG_SECOND_CONTROL_PANEL_IP = ************\r\n
             WR_TAG_MAIN_CONTROL_PANEL_MODE_FLAG = 1\r\n
             WR_TAG_PWR_DEC_DELTA_VAL = 0\r\n
             WR_TAG_AES_ALGORITHM_SWITCH = 0\r\n
             WR_TAG_REDIRECT_NUM_MAX = 10\r\nWR_TAG_REDIRECT_NODE_NUM_MAX = 10000\r\nWR_TAG_REDIRECT_NODE_TIME_MAX = 10\r\nWR_TAG_1907_CELL_STATUS = 3 \r\nWR_V2_SELECT_FREQ_0 = 8:39:0\r\nWR_V2_SELECT_FREQ_1 = 7:38:41\r\nWR_V2_SELECT_FREQ_2 = 3:9:0\r\nWR_V2_SELECT_FREQ_3 = 1:40:0\r\nWR_V3_SELECT_FREQ_1 = 38:41:0\r\nWR_V3_SELECT_FREQ_2 = 39:0:0\r\nWR_V3_SELECT_FREQ_3 = 40:0:0\r\nWR_V3_SELECT_FREQ_4 = 0:0:0\r\nWR_V3_SELECT_FREQ_5 = 0:0:0\r\nWR_V3_SELECT_FREQ_6 = 0:0:0\r\nWR_V3_SELECT_FREQ_7 = 0:0:0\r\nWR_V3_SELECT_FREQ_8 = 8:0:0\r\nWR_V3_SELECT_FREQ_9 = 1:0:0\r\nWR_V3_SELECT_FREQ_10 = 0:0:0\r\nWR_V3_SELECT_FREQ_11 = 3:9:0\r\nWR_V3_SELECT_FREQ_12 = 20:0:0\r\nWR_V3_SELECT_FREQ_13 = 5:0:0\r\nWR_V3_SELECT_FREQ_14 = 0:0:0\r\nWR_V3_SELECT_FREQ_15 = 7:0:0    \r\nWR_V5_SELECT_FREQ_1 = 38:41:0\r\nWR_V5_SELECT_FREQ_2 = 39:0:0\r\nWR_V5_SELECT_FREQ_3 = 40:0:0\r\nWR_V5_SELECT_FREQ_4 = 3:0:0\r\nWR_V5_SELECT_FREQ_5 = 1:9:0\r\nWR_V5_SELECT_FREQ_6 = 0:0:7\r\nWR_V5_SELECT_FREQ_7 = 5:0:0\r\nWR_V5_SELECT_FREQ_8 = 8:0:0\r\nWR_V5_SELECT_FREQ_9 = 0:0:0\r\nWR_V5_SELECT_FREQ_10 = 0:0:0\r\nWR_V5_SELECT_FREQ_11 = 0:0:0\r\nWR_V5_SELECT_FREQ_12 = 0:0:0\r\nWR_V5_SELECT_FREQ_13 = 0:0:0\r\nWR_V5_SELECT_FREQ_14 = 0:0:0\r\nWR_V5_SELECT_FREQ_15 = 0:0:0     \r\nCFGEND                      = 1\r\n#This CFGEND has to be kept at the end and should not be moved as the configuration\r\n#parameter reading ends once we read this tag. Any new configuration tag MUST be\r\n#added above CFGEND......",
